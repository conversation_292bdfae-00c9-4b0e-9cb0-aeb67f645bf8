<template>
  <q-page class="modern-landing-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
      </div>
      <div class="max-ctr">
        <div class="hero-content q-px-xl q-py-md">
          <div class="row items-center min-height-screen">
            <div class="col-12 col-md-6 q-pr-xl">
              <div class="hero-text"
                   style="margin-bottom:100px">
                <div class="hero-badge q-mb-md">
                  <q-chip color="accent"
                          text-color="white"
                          icon="sports_esports"
                          class="q-px-md">
                    NEW! Price Guessing Fun
                  </q-chip>
                </div>
                <h1 class="hero-title animate-slide-up text-accent">
                  The House Price
                  <span class="text-white"> Guessing Game</span>
                  <!-- Play & Learn! -->
                </h1>
                <p class="hero-subtitle animate-fade-in-delayed text-h6 q-mt-md q-mb-xl">
                  Create and play exciting property price challenges in minutes! Simply
                  enter a bunch of property listing urls to generate a game with real
                  listings. Share with friends and see who guesses closest!
                </p>
                <!-- <div class="hero-cta-buttons q-gutter-md">
                  <q-btn label="Create Your First Game!"
                         color="accent"
                         size="lg"
                         unelevated
                         rounded
                         class="cta-primary q-px-md q-mb-md"
                         @click="blurbCta">
                    <q-icon name="add_circle"
                            class="q-ml-sm" />
                  </q-btn>
                  <q-btn label="Play a Quick Game"
                         color="white"
                         text-color="white"
                         size="lg"
                         outline
                         rounded
                         class="cta-secondary q-px-md q-mb-md"
                         href="https://nuneaton.propertysquares.com/">
                    <q-icon name="play_circle_outline"
                            class="q-ml-sm" />
                  </q-btn>
                </div> -->
              </div>
            </div>
            <div class="col-12 col-md-6">
              <div class="hero-visual q-mt-xl">
                <div class="property-card-demo animate-float">
                  <q-card class="demo-card shadow-10">
                    <q-img src="https://images.unsplash.com/photo-1621983209348-7b5a63f23866?w=400&h=250&fit=crop"
                           height="200px"
                           class="rounded-borders-top">
                      <div class="absolute-top-right q-ma-sm">
                        <q-chip color="orange"
                                text-color="white"
                                dense>
                          <q-icon name="quiz"
                                  size="sm"
                                  class="q-mr-xs" />
                          Price Challenge!
                        </q-chip>
                      </div>
                    </q-img>
                    <q-card-section>
                      <div class="text-h6 text-weight-bold">Large Semi Detached</div>
                      <div class="text-subtitle2 text-grey-6 q-mb-sm">
                        Birmingham, B12
                      </div>
                      <div class="row items-center q-gutter-sm q-mb-md">
                        <q-chip size="sm"
                                outline
                                color="primary">3 beds</q-chip>
                        <q-chip size="sm"
                                outline
                                color="primary">2 baths</q-chip>
                        <q-chip size="sm"
                                outline
                                color="primary">Garage</q-chip>
                      </div>
                      <div class="text-h5 text-weight-bold text-accent q-mb-sm">
                        What's Your Guess?
                      </div>
                      <q-input outlined
                               dense
                               label="Enter Price (e.g., $450,000)"
                               disable />
                    </q-card-section>
                    <q-card-actions align="right"
                                    class="q-pa-md">
                      <q-btn color="accent"
                             label="Submit Guess"
                             unelevated
                             rounded
                             disable />
                    </q-card-actions>
                  </q-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Available Games Section -->
    <section class="available-games-section q-py-xl bg-white">
      <div class="max-ctr q-px-sm">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Available Games
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Jump right into one of our ready-to-play property price guessing games!
          </p>
        </div>

        <ScootGamesSummary :subdomain-name="subdomainName"
                           :auto-load="true"
                           :availableHpgGames="availableHpgGames"
                           @game-selected="handleGameSelected"
                           @games-loaded="handleGamesLoaded" />
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Why You'll Love <span class="text-accent">The Guessing Game</span>
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Discover a fun, social, and insightful way to explore the property market.
          </p>
        </div>

        <div class="row q-col-gutter-xl">
          <div class="col-12 col-md-4"
               v-for="feature in features"
               :key="feature.title">
            <q-card class="feature-card h-full hover-lift"
                    flat
                    bordered>
              <q-card-section class="text-center q-pa-xl">
                <div class="feature-icon q-mb-lg">
                  <q-avatar size="80px"
                            :color="feature.color"
                            text-color="white">
                    <q-icon :name="feature.icon"
                            size="40px" />
                  </q-avatar>
                </div>
                <h3 class="text-h5 text-weight-bold q-mb-md">{{ feature.title }}</h3>
                <p class="text-body1 text-grey-7">{{ feature.description }}</p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works-section q-py-xl bg-white">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Get Guessing in 3 Simple Steps
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Creating and sharing your property price challenge is quick and easy.
          </p>
        </div>

        <div class="row q-col-gutter-xl items-stretch">
          <div class="col-12 col-md-4"
               v-for="(step, index) in howItWorksSteps"
               :key="step.title">
            <div class="step-card text-center q-pa-lg">
              <div class="step-number-badge q-mb-lg">
                <q-avatar size="60px"
                          color="accent"
                          text-color="white"
                          class="text-h4 text-weight-bold">
                  {{ index + 1 }}
                </q-avatar>
              </div>
              <div class="step-icon q-mb-md">
                <q-icon :name="step.icon"
                        size="48px"
                        :color="step.color" />
              </div>
              <h3 class="text-h5 text-weight-bold q-mb-md">{{ step.title }}</h3>
              <p class="text-body1 text-grey-7">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Proof Section -->
    <section class="social-proof-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            What Players Are Saying
          </h2>
          <p class="text-h6 text-grey-7">
            Join the fun and see why everyone's hooked on guessing house prices!
          </p>
        </div>

        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-4"
               v-for="testimonial in testimonials"
               :key="testimonial.name">
            <q-card class="testimonial-card h-full"
                    flat
                    bordered>
              <a :href="testimonial.testimonial_url">
                <q-card-section class="q-pa-lg">
                  <div class="row items-center q-mb-md">
                    <!-- <q-rating v-model="testimonial.rating"
                            readonly
                            color="yellow-8"
                            size="sm" /> -->
                  </div>
                  <p class="text-body1 q-mb-md">"{{ testimonial.quote }}"</p>
                  <div class="row items-center">
                    <q-avatar size="40px"
                              color="primary"
                              text-color="white">
                      {{ testimonial.name.charAt(0) }}
                    </q-avatar>
                    <div class="q-ml-md">
                      <div class="text-weight-bold">{{ testimonial.name }}</div>
                      <!-- <div class="text-caption text-grey-6">{{ testimonial.role }}</div> -->
                    </div>
                  </div>
                </q-card-section>
              </a>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section q-py-xl bg-primary text-white">
      <div class="max-ctr q-px-xl">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-pr-xl">
            <h2 class="text-h3 text-weight-bold q-mb-md">More Than Just a Game</h2>
            <p class="text-h6 q-mb-lg">
              Turn real estate exploration into an engaging, social, and educational
              experience for everyone—from aspiring buyers to seasoned pros.
            </p>
            <div class="benefits-list">
              <div class="benefit-item q-mb-md"
                   v-for="benefit in keyBenefits"
                   :key="benefit">
                <q-icon name="check_circle"
                        color="accent"
                        size="sm"
                        class="q-mr-sm" />
                <span class="text-body1">{{ benefit }}</span>
              </div>
            </div>
            <!-- <q-btn label="Start Playing Now"
                   color="accent"
                   @click="blurbCta"
                   size="lg"
                   unelevated
                   rounded
                   class="q-my-lg q-px-md" /> -->
          </div>
          <div class="col-12 col-md-6">
            <div class="benefits-visual">
              <!-- Kept original image, ideally replace with one showing people playing or a game interface -->
              <q-img src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=500&h=400&fit=crop"
                     class="rounded-borders shadow-5"
                     style="max-width: 100%" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Frequently Asked Questions
          </h2>
          <p class="text-h6 text-grey-7">
            Your questions answered about The House Price Guessing Game.
          </p>
        </div>
        <div class="row justify-center">
          <div class="col-12 col-md-8">
            <q-list class="faq-list">
              <q-no-ssr>
                <q-expansion-item v-for="(faq, index) in faqs"
                                  :key="index"
                                  :label="faq.question"
                                  header-class="text-h6 text-weight-medium"
                                  expand-icon-class="text-accent"
                                  class="faq-item q-mb-md">
                  <template v-slot:header>
                    <q-item-section>
                      <q-item-label class="text-h6 text-weight-medium">
                        {{ faq.question }}
                      </q-item-label>
                    </q-item-section>
                  </template>
                  <q-card flat
                          class="bg-white">
                    <q-card-section class="q-pa-lg">
                      <p class="text-body1 text-grey-8">{{ faq.answer }}</p>
                    </q-card-section>
                  </q-card>
                </q-expansion-item>
              </q-no-ssr>
            </q-list>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <!-- <section class="final-cta-section q-py-xl bg-gradient-modern text-white">
      <div class="max-ctr q-px-xl">
        <div class="text-center">
          <h2 class="text-h3 text-weight-bold q-mb-md">
            Ready to Test Your Guessing Skills?
          </h2>
          <p class="text-h5 q-mb-xl max-width-md mx-auto">
            Create your first game now, challenge your friends, and discover who's the
            ultimate property price guru! Fun, free, and fantastic for learning.
          </p>
          <div class="cta-buttons q-gutter-md">
            <q-btn label="Create a Game Now!"
                   color="white"
                   text-color="primary"
                   size="xl"
                   unelevated
                   rounded
                   @click="blurbCta"
                   class="q-px-md q-py-md">
              <q-icon name="add_circle"
                      class="q-ml-sm" />
            </q-btn>
            <q-btn label="Play Demo Game"
                   color="transparent"
                   text-color="white"
                   size="xl"
                   outline
                   rounded
                   class="q-px-md q-py-md"
                   href="https://nuneaton.propertysquares.com/">
              <q-icon name="sports_esports"
                      class="q-ml-sm" />
            </q-btn>
          </div>
        </div>
      </div>
    </section> -->

    <!-- <div class="cta-features q-mt-xl">
            <div class="row justify-center q-gutter-lg">
              <div class="feature-badge">
                <q-icon name="check" color="accent" size="sm" class="q-mr-xs" />
                <span class="text-body2">No credit card required</span>
              </div>
              <div class="feature-badge">
                <q-icon name="check" color="accent" size="sm" class="q-mr-xs" />
                <span class="text-body2">14-day free trial</span>
              </div>
              <div class="feature-badge">
                <q-icon name="check" color="accent" size="sm" class="q-mr-xs" />
                <span class="text-body2">Cancel anytime</span>
              </div>
            </div>
          </div> -->

    <!-- Attribution Section -->
    <CreativeCommonsAttribution license-type="by-2.5"
                                source="Wikipedia"
                                custom-prefix="Game background images are sourced from Wikipedia and used under the " />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import ScootGamesSummary from 'src/concerns/psq/components/ScootGamesSummary.vue'
import CreativeCommonsAttribution from 'src/components/common/CreativeCommonsAttribution.vue'
import useScoots from "src/compose/useScoots.js"


const { getScootForRealtyGames } = useScoots()
const availableHpgGames = ref([])


const getInitialScoot = async () => {
  try {
    const response = await getScootForRealtyGames("hpg-scoot");
    // scootFromLandingPage.value = response.data?.scoot;
    // if (response.data?.scoot?.available_games_details) {
    //   availableGamesDetails.value = response.data?.scoot?.available_games_details
    // }
    availableHpgGames.value = response.data?.scoot?.available_games_details
    // if (response.data?.scoot?.is_price_guess_only) {
    //   isDossier.value = false
    //   isPriceGuess.value = true
    //   //  response.data.scoot.is_price_guess_only;
    // }
    // else {
    //   isPriceGuess.value = false
    //   isDossier.value = true
    // }
    // if (response.data.error) {
    //   errorMessage.value = response.data.error;
    // }
  } catch (error) {
    console.error('Error fetching initial scoot:', error);
  }
};

onMounted(() => {
  // checkLocalStorageForAccess();
  getInitialScoot();
});

// Composables
const $q = useQuasar()
const $router = useRouter()

// Emits
const emit = defineEmits(['blurbCta'])

// Computed properties
const subdomainName = computed(() => {
  // Extract subdomain from current URL or use a default
  let hostname = ""
  if (typeof window !== "undefined") {
    hostname = window.location.hostname
  }
  const parts = hostname.split('.')
  return parts.length > 2 ? parts[0] : 'hpg-scoot'
})

// Data
const features = ref([
  {
    title: "Create Games in Minutes",
    description:
      "Simply enter a bunch of property listing urls to generate a unique guessing game with real listings.",
    icon: "edit_location_alt",
    color: "accent",
  },
  {
    title: "Fun for Everyone",
    description:
      "Challenge friends, family, or colleagues. Perfect for learning or friendly competition.",
    icon: "groups",
    color: "primary",
  },
  {
    title: "Learn as You Play",
    description:
      "Sharpen your property price knowledge and market intuition by guessing prices on diverse listings with photos and descriptions.",
    icon: "lightbulb",
    color: "secondary",
  },
])

const howItWorksSteps = ref([
  {
    title: "1. Create Your Game",
    description:
      "Define your game: just enter the listings to be used. We'll generate the game!",
    icon: "add_circle_outline",
    color: "primary",
  },
  {
    title: "2. Share the Challenge",
    description:
      "Get a unique link for your game. Share it instantly with friends, family, or on social media.",
    icon: "share",
    color: "accent",
  },
  {
    title: "3. Guess & Compete!",
    description:
      "Players submit their price guesses. See who gets closest and earns bragging rights!",
    icon: "emoji_events",
    color: "secondary",
  },
])

const testimonials = ref([
  {
    name: "Reddit user",
    role: "Property investor",
    testimonial_url: "https://www.reddit.com/r/brum/comments/1l5jddf/how_good_are_you_at_guessing_house_prices_in/",
    quote:
      "This was fun. The Moseley flat’s price really surprised me, I can’t believe it’s nearly double to live near Spark Hill 😬",
    rating: 5,
  },
  {
    name: "Reddit user",
    role: "Property investor",
    testimonial_url: "https://www.reddit.com/r/coventry/comments/1lfyiqh/how_good_are_you_at_guessing_house_prices_in_cov/",
    quote:
      "Ps I absolutely love this and would play it until there were no more houses left on rightmove!",
    rating: 5,
  },
  {
    name: "Reddit user",
    role: "Property investor",
    testimonial_url: "https://www.reddit.com/r/coventry/comments/1lfyiqh/how_good_are_you_at_guessing_house_prices_in_cov/",
    quote:
      "That was really fun!!! Thanks so much! I got 580 / 600, I’m currently in the process of buying a house and check Rightmove everyday, which I credit for my great success 😂",
    rating: 5,
  },
  // {
  //   name: "Maria S.",
  //   role: "Aspiring Home Buyer",
  //   quote:
  //     "I'm actually learning so much about local property values. It's way more engaging than just browsing listings.",
  //   rating: 4.5,
  // },
  // {
  //   name: "John B.",
  //   role: "Real Estate Agent",
  //   quote:
  //     "A fantastic icebreaker and a fun way to engage clients and help them understand market pricing. Highly recommend!",
  //   rating: 5,
  // },
])

const keyBenefits = ref([
  "Get people interested in your house hunt.",
  "Sharpen your property valuation skills.",
  "Engage clients or social media followers creatively.",
  "Quick to set up, easy to play and share.",
])

const faqs = ref([
  {
    question: "What is The House Price Guessing Game?",
    answer:
      "It's a fun and innovative website where anyone can create and play exciting property price challenges in minutes! Simply enter a bunch of property listing urls to generate a game with real listings, complete with photos and descriptions.",
  },
  {
    question: "How do I create a game?",
    answer:
      "At the end of every game you play you will get the chance to fill out a simple form to create your own price guessing game."
    // "It's easy! Just input a location you're interested in, select a property type (e.g., house, apartment), and define a price range. The game will then generate property listings for your challenge.",
  },
  {
    question: "Who can I play with?",
    answer:
      "Share your game with anyone—friends, family, colleagues, or clients—via a link. No account needed to join the fun and compete!",
  },
  {
    question: "Is it free to play and create games?",
    answer:
      "Yes! The House Price Guessing Game is free to use. You can create games, share them with friends, and play as much as you like without any cost.",
  },
  {
    question: "Who is this game for?",
    answer:
      "It's for everyone! Aspiring home buyers, curious learners, seasoned real estate pros, realtors looking for client engagement tools, or simply groups of friends wanting a fun new game. It's great for when you are house hunting or wanting to learn more about the property market.",
  },
])

// Methods
const handleGameSelected = (game) => {
  $q.notify({
    type: 'positive',
    message: `Starting ${game.game_title}!`,
    position: 'top'
  })

  // Navigate to the game
  $router.push({
    name: 'rPriceGameStart',
    params: { gameSlug: game.realty_game_slug }
  })
}

const handleGamesLoaded = (games) => {
  console.log('Games loaded:', games)
}

const blurbCta = () => {
  emit('blurbCta')
}
</script>

<style scoped>
/* Modern Landing Page Styles */
.modern-landing-page {
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
  opacity: 0.95;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%,
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 60px 60px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.6;
}

.min-height-screen {
  min-height: 100vh;
}

/* Animations */
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-delayed {
  animation: fadeInDelayed 1s ease-out 0.3s forwards;
  opacity: 0;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelayed {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

/* CTA Buttons */
.cta-primary {
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4);
}

.cta-secondary {
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Demo Card */
.demo-card {
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: all 0.3s ease;
}

.demo-card:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

/* Feature Cards */
.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(31, 51, 147, 0.1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Testimonial Cards */
.testimonial-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(31, 51, 147, 0.1);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* FAQ Section */
.faq-item {
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(31, 51, 147, 0.1);
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Final CTA */
.bg-gradient-modern {
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
}

.feature-badge {
  display: flex;
  align-items: center;
  opacity: 0.9;
}

/* Utility Classes */
.max-width-md {
  max-width: 600px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.h-full {
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .min-height-screen {
    min-height: auto;
    padding: 4rem 0;
  }

  .demo-card {
    transform: none;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .q-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .stat-item {
    text-align: center;
  }

  .feature-badge {
    justify-content: center;
  }
}
</style>
